import React, { useEffect, useState, useSyncExternalStore } from "react";
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Flame } from "lucide-react";
import { getKilns, KilnConfig, FireConfig } from "@/data/kilnData";
import { getSettingProductionEntries, subscribeToSettingProduction, SettingProductionEntry } from "@/data/settingProductionStore";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { useChamberFireStatus, useUpdateChamberFireStatus } from "@/hooks/useChamberFireStatus";
import { toast } from "sonner";

type ChamberSummary = {
  chamberId: string;
  chamberName: string;
  setBricks: number;
  dehackedBricks: number;
};

function aggregateChamberSummary(
  fires: FireConfig[],
  brickTypes: ManagementBrickType[],
  settingProductionEntries: SettingProductionEntry[],
) {
  // chamberId = fire.id

  // Gather per chamber: {chamberId, chamberName, setBricks, dehackedBricks}
  return fires.map((fire) => {
    // Setting
    const setEntries = settingProductionEntries.filter(e => e.fire_id === fire.id);
    const setBricks = setEntries.reduce((sum, entry) => {
      const brickType = brickTypes.find(bt => bt.id === entry.brick_type_id);
      return sum + (brickType ? brickType.bricks_per_pallet * entry.pallet_count : 0);
    }, 0);
    // Dehacking cannot be calculated per chamber with current schema.
    const dehackedBricks = 0;

    return {
      chamberId: fire.id,
      chamberName: fire.name,
      setBricks,
      dehackedBricks,
    };
  });
}

export const ChamberDashboard = () => {
  const [kilns, setKilns] = useState<KilnConfig[]>([]);
  const [brickTypes, setBrickTypes] = useState<ManagementBrickType[]>([]);
  const [loading, setLoading] = useState(true);

  const settingEntries = useSyncExternalStore(subscribeToSettingProduction, getSettingProductionEntries);
  const { data: chamberFireStatus = [], isLoading: fireStatusLoading } = useChamberFireStatus();
  const updateChamberFireStatusMutation = useUpdateChamberFireStatus();

  useEffect(() => {
    async function loadData() {
      setLoading(true);
      const [kilnsResult, brickTypesResult] = await Promise.all([
        getKilns(),
        getManagementBrickTypes(),
      ]);
      setKilns(kilnsResult);
      setBrickTypes(brickTypesResult);
      setLoading(false);
    }
    loadData();
  }, []);

  // Helper function to get fire status for a specific chamber
  const getChamberFireStatus = (kilnId: string, chamberNumber: number): boolean => {
    const status = chamberFireStatus.find(
      s => s.kiln_id === kilnId && s.chamber_number === chamberNumber
    );
    return status?.is_burning || false;
  };

  // Handle toggle switch change
  const handleFireToggle = (kilnId: string, chamberNumber: number, isBurning: boolean) => {
    console.log("🎯 Toggle clicked:", { kilnId, chamberNumber, isBurning });

    updateChamberFireStatusMutation.mutate(
      { kilnId, chamberNumber, isBurning },
      {
        onSuccess: () => {
          // Check if localStorage was used (indicated by timestamp-based storage)
          const localStorageData = JSON.parse(localStorage.getItem('chamber_fire_status') || '[]');
          const hasLocalData = localStorageData.length > 0;

          if (hasLocalData) {
            toast.success(`Chamber ${chamberNumber} fire status updated! (Stored locally until database table is created)`);
          } else {
            toast.success(`Chamber ${chamberNumber} fire status updated`);
          }
        },
        onError: (error) => {
          console.error("❌ Toggle error:", error);
          toast.error(`Failed to update fire status: ${error.message}`);
        },
      }
    );
  };

  if (loading) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Chamber Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-slate-500">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {kilns.map(kiln => {
        const chambers = kiln.fires;
        // Always display 12 chambers, fill with blanks if less
        const CHAMBER_COUNT = 12;
        const chambersList = [
          ...chambers,
          ...Array(CHAMBER_COUNT - chambers.length).fill(null),
        ].slice(0, CHAMBER_COUNT);

        // Get summary
        const summaryById = aggregateChamberSummary(chambers, brickTypes, settingEntries);
        // Fill empty chambers with blanks for grid
        const chamberData: (ChamberSummary | null)[] = chambersList.map((fire, idx) =>
          fire
            ? summaryById.find(cs => cs.chamberId === fire.id) || {
                chamberId: fire.id,
                chamberName: fire.name,
                setBricks: 0,
                dehackedBricks: 0,
              }
            : null
        );
        return (
          <Card key={kiln.id}>
            <CardHeader>
              <CardTitle>{kiln.name} - Chamber Dashboard</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Setting Table */}
              <h3 className="font-semibold text-slate-800 mb-2">Setting</h3>
              <div className="grid grid-cols-4 gap-4 mb-4">
                {chamberData.map((chamber, idx) => {
                  const chamberNumber = idx + 1;
                  const isBurning = getChamberFireStatus(kiln.id, chamberNumber);

                  return (
                    <div
                      key={chamber ? chamber.chamberId : idx}
                      className={`border rounded p-3 min-h-[80px] text-center flex flex-col items-center justify-between ${
                        isBurning ? 'bg-orange-50 border-orange-200' : 'bg-slate-50'
                      }`}
                    >
                      <div className="flex flex-col items-center flex-1">
                        <div className="text-sm font-medium mb-1">
                          {chamber
                            ? `Chamber ${chamberNumber}${chamber.chamberName ? ` (${chamber.chamberName})` : ""}`
                            : `Chamber ${chamberNumber}`}
                        </div>
                        <div className="text-blue-700 font-bold text-xl mb-2">
                          {chamber?.setBricks.toLocaleString() || "-"}
                        </div>
                      </div>

                      {/* Fire Toggle Switch */}
                      <div className="flex items-center gap-2 mt-2">
                        <Flame
                          size={16}
                          className={isBurning ? "text-orange-500" : "text-slate-400"}
                        />
                        <Switch
                          checked={isBurning}
                          onCheckedChange={(checked) => handleFireToggle(kiln.id, chamberNumber, checked)}
                          disabled={updateChamberFireStatusMutation.isPending}
                          className="data-[state=checked]:bg-orange-500"
                        />
                        <Label className="text-xs text-slate-600">
                          {isBurning ? "Burning" : "Off"}
                        </Label>
                      </div>
                    </div>
                  );
                })}
              </div>
              {/* Dehacking Table */}
              <h3 className="font-semibold text-slate-800 mb-2">Dehacking</h3>
              <div className="grid grid-cols-4 gap-4">
                {chamberData.map((chamber, idx) => {
                  const chamberNumber = idx + 1;
                  const isBurning = getChamberFireStatus(kiln.id, chamberNumber);

                  return (
                    <div
                      key={chamber ? chamber.chamberId : idx}
                      className={`border rounded p-3 min-h-[80px] text-center flex flex-col items-center justify-between ${
                        isBurning ? 'bg-orange-50 border-orange-200' : 'bg-slate-50'
                      }`}
                    >
                      <div className="flex flex-col items-center flex-1">
                        <div className="text-sm font-medium mb-1">
                          {chamber
                            ? `Chamber ${chamberNumber}${chamber.chamberName ? ` (${chamber.chamberName})` : ""}`
                            : `Chamber ${chamberNumber}`}
                        </div>
                        <div className="text-green-700 font-bold text-xl mb-2">
                          {chamber?.dehackedBricks.toLocaleString() || "-"}
                        </div>
                      </div>

                      {/* Fire Status Indicator (read-only for dehacking) */}
                      <div className="flex items-center gap-2 mt-2">
                        <Flame
                          size={16}
                          className={isBurning ? "text-orange-500" : "text-slate-400"}
                        />
                        <Label className="text-xs text-slate-600">
                          {isBurning ? "Burning" : "Off"}
                        </Label>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default ChamberDashboard;
