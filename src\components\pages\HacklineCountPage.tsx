import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calculator, Plus, Calendar, Hash, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useHacklineCounts, useAddHacklineCount } from "@/hooks/useHacklineCounts";
import { format } from "date-fns";

// Form validation schema
const hacklineCountSchema = z.object({
  date: z.string().min(1, "Date is required"),
  count_total: z.number().min(1, "Count must be at least 1"),
  notes: z.string().optional(),
});

type HacklineCountFormValues = z.infer<typeof hacklineCountSchema>;

export const HacklineCountPage = () => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  
  const { data: hacklineCounts = [], isLoading } = useHacklineCounts();
  const addHacklineCountMutation = useAddHacklineCount();

  const form = useForm<HacklineCountFormValues>({
    resolver: zodResolver(hacklineCountSchema),
    defaultValues: {
      date: format(new Date(), "yyyy-MM-dd"),
      count_total: 0,
      notes: "",
    },
  });

  const onSubmit = (data: HacklineCountFormValues) => {
    addHacklineCountMutation.mutate(data, {
      onSuccess: () => {
        toast.success("Hackline count recorded successfully!");
        form.reset({
          date: format(new Date(), "yyyy-MM-dd"),
          count_total: 0,
          notes: "",
        });
        setIsFormVisible(false);
      },
      onError: (error) => {
        toast.error("Failed to record hackline count");
        console.error("Error adding hackline count:", error);
      },
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Hackline Count</h1>
          <p className="text-slate-600">Track bricks drying in the sun</p>
        </div>
        <Button 
          onClick={() => setIsFormVisible(!isFormVisible)}
          className="flex items-center gap-2"
        >
          <Plus size={20} />
          {isFormVisible ? "Cancel" : "Add Count"}
        </Button>
      </div>

      {/* Add Count Form */}
      {isFormVisible && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator size={20} />
              Record Hackline Count
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date" className="flex items-center gap-2">
                    <Calendar size={16} />
                    Date
                  </Label>
                  <Input
                    id="date"
                    type="date"
                    {...form.register("date")}
                    className="w-full"
                  />
                  {form.formState.errors.date && (
                    <p className="text-sm text-red-600">{form.formState.errors.date.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="count_total" className="flex items-center gap-2">
                    <Hash size={16} />
                    Total Count
                  </Label>
                  <Input
                    id="count_total"
                    type="number"
                    min="1"
                    placeholder="Enter brick count"
                    {...form.register("count_total", { valueAsNumber: true })}
                    className="w-full"
                  />
                  {form.formState.errors.count_total && (
                    <p className="text-sm text-red-600">{form.formState.errors.count_total.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add any additional notes about the count..."
                  rows={3}
                  {...form.register("notes")}
                  className="resize-none"
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  type="submit"
                  disabled={addHacklineCountMutation.isPending}
                  className="flex items-center gap-2"
                >
                  {addHacklineCountMutation.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                  Record Count
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsFormVisible(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Recent Counts */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Hackline Counts</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
            </div>
          ) : hacklineCounts.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <Calculator className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No hackline counts recorded yet.</p>
              <p className="text-sm">Click "Add Count" to record your first count.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {hacklineCounts.slice(0, 10).map((count) => (
                <div
                  key={count.id}
                  className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border"
                >
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar size={16} className="text-slate-500" />
                      <span className="font-medium">{format(new Date(count.date), "MMM dd, yyyy")}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Hash size={16} className="text-slate-500" />
                      <span className="text-lg font-semibold text-slate-800">{count.count_total.toLocaleString()}</span>
                      <span className="text-sm text-slate-500">bricks</span>
                    </div>
                  </div>
                  <div className="text-sm text-slate-500">
                    {format(new Date(count.created_at), "HH:mm")}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
