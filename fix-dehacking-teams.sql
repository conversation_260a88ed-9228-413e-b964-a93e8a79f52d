-- ========================================
-- FIX DEHACKING TEAMS ISSUE
-- ========================================
-- Copy and paste this ENTIRE script into your Supabase SQL Editor and click RUN

-- Step 1: Check current state
SELECT 'Current teams:' as info;
SELECT * FROM public.teams ORDER BY name;

-- Step 2: Ensure teams table exists
CREATE TABLE IF NOT EXISTS public.teams (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL
);

-- Step 3: Add team_id column to dehacking_entries if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dehacking_entries'
                   AND column_name = 'team_id') THEN
        ALTER TABLE public.dehacking_entries
        ADD COLUMN team_id TEXT REFERENCES public.teams(id);
        RAISE NOTICE 'Added team_id column to dehacking_entries';
    ELSE
        RAISE NOTICE 'team_id column already exists in dehacking_entries';
    END IF;
END $$;

-- Step 4: Create the two specific dehacking teams
INSERT INTO public.teams (id, name) VALUES
('dehacking-team-mercy', 'Dehacking Team Mercy'),
('dehacking-team-taurai', 'Dehacking Team Taurai')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name;

-- Step 5: Enable RLS on teams table if not already enabled
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;

-- Step 6: Create policies for teams table
DROP POLICY IF EXISTS "Allow public access to teams" ON public.teams;
CREATE POLICY "Allow public access to teams" ON public.teams FOR ALL USING (true) WITH CHECK (true);

-- Step 7: Verify the teams were created
SELECT 'Dehacking teams created:' as info;
SELECT * FROM public.teams WHERE id IN ('dehacking-team-mercy', 'dehacking-team-taurai');

-- Step 8: Check dehacking_entries table structure
SELECT 'Dehacking entries table structure:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'dehacking_entries' 
ORDER BY ordinal_position;

-- Step 9: Test insert (this should work now)
SELECT 'Testing insert capability...' as info;
-- Note: This is just a test to see if the structure is correct
-- The actual insert will be done by the application
