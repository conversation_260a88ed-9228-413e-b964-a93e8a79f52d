
import { useState } from "react";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { useUser } from "@/contexts/UserContext";
import { useDashboardLayout } from "@/contexts/DashboardLayoutContext";
import { FactoryOutputCard } from "./cards/FactoryOutputCard";
import { SettingTeamsCard } from "./cards/SettingTeamsCard";
import { DehackingCard } from "./cards/DehackingCard";
import { FuelManagementCard } from "./cards/FuelManagementCard";
import { PalletTrackingCard } from "./cards/PalletTrackingCard";
import { ProductionEntryDialog } from "./dialogs/ProductionEntryDialog";
import { SettingProductionEntryDialog } from "./dialogs/SettingProductionEntryDialog";
import { FuelActionChoiceDialog } from "./dialogs/FuelActionChoiceDialog";
import { RecordFuelDispensingDialog } from "./dialogs/RecordFuelDispensingDialog";
import { RecordFuelDeliveryDialog } from "./dialogs/RecordFuelDeliveryDialog";
import { DispenseFuelDialog } from "./dialogs/DispenseFuelDialog";
import { TrackPalletDialog } from "@/components/pallet-tracking/TrackPalletDialog";
import { useFuelBunkers } from "@/hooks/useFuelBunkers";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getAllPalletMovements, createPalletMovement, recordPalletReturn, updatePalletMovementStatus } from "@/data/palletTrackingData";

// Mock data for dialogs (REMOVE mockBunkers)
const mockAssets = [
  { id: "asset1", name: "Kiln A" },
  { id: "asset2", name: "Kiln B" },
  { id: "asset3", name: "Forklift FL001" },
  { id: "asset4", name: "Generator G002" },
  { id: "asset5", name: "Truck T003" },
];

export const QuickAccessCards = () => {
  const queryClient = useQueryClient();
  const { canAccessDashboardCard } = useUser();
  const { getGridClassName } = useDashboardLayout();
  const [isProductionDialogOpen, setIsProductionDialogOpen] = useState(false);
  const [isSettingProductionDialogOpen, setIsSettingProductionDialogOpen] = useState(false);

  const [isFuelActionChoiceOpen, setIsFuelActionChoiceOpen] = useState(false);
  const [isRecordDispensingOpen, setIsRecordDispensingOpen] = useState(false);
  const [isRecordDeliveryOpen, setIsRecordDeliveryOpen] = useState(false);
  const [isTrackPalletDialogOpen, setIsTrackPalletDialogOpen] = useState(false);

  // --- New: fetch the bunkers from the database
  const { data: dbBunkers = [] } = useFuelBunkers();

  // Pallet tracking data and mutations
  const { data: movements = [] } = useQuery({
    queryKey: ["pallet-movements"],
    queryFn: getAllPalletMovements,
  });

  const [outgoingForm, setOutgoingForm] = useState({
    delivery_date: "",
    delivery_note: "",
    vehicle_registration: "",
    product_type: "",
    destination: "",
    pallets_loaded: 0,
    comments: "",
  });

  const [returnForm, setReturnForm] = useState({
    pallet_movement_id: "",
    return_date: "",
    pallets_returned: 0,
    condition: "",
    comments: "",
  });

  const outgoingMutation = useMutation({
    mutationFn: createPalletMovement,
    onSuccess: () => {
      toast.success("Outgoing pallets recorded successfully");
      queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
      setOutgoingForm({
        delivery_date: "",
        delivery_note: "",
        vehicle_registration: "",
        product_type: "",
        destination: "",
        pallets_loaded: 0,
        comments: "",
      });
      setIsTrackPalletDialogOpen(false);
    },
    onError: (error: any) => {
      toast.error("Failed to record outgoing pallets: " + error.message);
    },
  });

  const returnMutation = useMutation({
    mutationFn: recordPalletReturn,
    onSuccess: () => {
      toast.success("Returned pallets recorded successfully");
      queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
      setReturnForm({
        pallet_movement_id: "",
        return_date: "",
        pallets_returned: 0,
        condition: "",
        comments: "",
      });
      setIsTrackPalletDialogOpen(false);
    },
    onError: (error: any) => {
      toast.error("Failed to record returned pallets: " + error.message);
    },
  });

  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: any }) =>
      updatePalletMovementStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
    },
  });

  const handleSuccess = (queryKeys: string[]) => {
    queryKeys.forEach(key => {
      queryClient.invalidateQueries({ queryKey: [key] });
    });
  };

  const handleRecordProductionClick = () => {
    setIsProductionDialogOpen(true);
  };

  const handleRecordSettingProductionClick = () => {
    setIsSettingProductionDialogOpen(true);
  };

  const handleManageFuel = () => {
    toast.dismiss(); // Dismiss any existing toasts
    setIsFuelActionChoiceOpen(true);
  };

  const handleManagePallets = () => {
    setIsTrackPalletDialogOpen(true);
  };

  const handleOutgoingChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setOutgoingForm(prev => ({
      ...prev,
      [name]: name === "pallets_loaded" ? parseInt(value) || 0 : value,
    }));
  };

  const handleOutgoingSubmit = () => {
    if (!outgoingForm.delivery_date || !outgoingForm.delivery_note || !outgoingForm.vehicle_registration) {
      toast.error("Please fill in all required fields");
      return;
    }
    outgoingMutation.mutate(outgoingForm);
  };

  const handleReturnChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setReturnForm(prev => ({
      ...prev,
      [name]: name === "pallets_returned" ? parseInt(value) || 0 : value,
    }));
  };

  const handleReturnSelectChange = (field: keyof typeof returnForm, value: string) => {
    setReturnForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleReturnSubmit = () => {
    if (!returnForm.pallet_movement_id || !returnForm.return_date || returnForm.pallets_returned <= 0) {
      toast.error("Please fill in all required fields");
      return;
    }
    returnMutation.mutate(returnForm);
  };

  const openRecordDispensingDialog = () => {
    setIsFuelActionChoiceOpen(false);
    setIsRecordDispensingOpen(true);
  };

  const openRecordDeliveryDialog = () => {
    setIsFuelActionChoiceOpen(false);
    setIsRecordDeliveryOpen(true);
  };

  return (
    <>
      <div className={`grid ${getGridClassName()}`}>
        {canAccessDashboardCard('factory-output') && (
          <FactoryOutputCard onRecordProduction={handleRecordProductionClick} />
        )}
        {canAccessDashboardCard('setting-teams') && (
          <SettingTeamsCard onRecordProduction={handleRecordSettingProductionClick} />
        )}
        {canAccessDashboardCard('dehacking') && (
          <DehackingCard />
        )}
        {canAccessDashboardCard('fuel-management') && (
          <FuelManagementCard onManageFuel={handleManageFuel} />
        )}
        {canAccessDashboardCard('pallet-tracking') && (
          <PalletTrackingCard onManagePallets={handleManagePallets} />
        )}
      </div>

      <ProductionEntryDialog 
        isOpen={isProductionDialogOpen}
        onClose={() => setIsProductionDialogOpen(false)}
        onSuccess={() => handleSuccess(['setting_production_entries', 'settingTeamsCardSummary', 'settingSummary'])}
      />

      <SettingProductionEntryDialog
        isOpen={isSettingProductionDialogOpen}
        onClose={() => setIsSettingProductionDialogOpen(false)}
        onSuccess={() => handleSuccess(['settingTeamsCardSummary'])}
      />

      <FuelActionChoiceDialog
        isOpen={isFuelActionChoiceOpen}
        onClose={() => setIsFuelActionChoiceOpen(false)}
        onRecordDispensing={openRecordDispensingDialog}
        onRecordDelivery={openRecordDeliveryDialog}
      />

      <DispenseFuelDialog
        isOpen={isRecordDispensingOpen}
        onClose={() => setIsRecordDispensingOpen(false)}
      />

      <RecordFuelDeliveryDialog
        isOpen={isRecordDeliveryOpen}
        onClose={() => setIsRecordDeliveryOpen(false)}
        bunkers={dbBunkers.map(b => ({
          id: b.id,
          name: b.name,
        }))}
      />

      <TrackPalletDialog
        isOpen={isTrackPalletDialogOpen}
        onOpenChange={setIsTrackPalletDialogOpen}
        movements={movements}
        outgoingForm={outgoingForm}
        handleOutgoingChange={handleOutgoingChange}
        handleOutgoingSubmit={handleOutgoingSubmit}
        returnForm={returnForm}
        handleReturnChange={handleReturnChange}
        handleReturnSelectChange={handleReturnSelectChange}
        handleReturnSubmit={handleReturnSubmit}
        outgoingMutation={outgoingMutation}
        returnMutation={returnMutation}
        updateStatusMutation={updateStatusMutation}
      />
    </>
  );
};
