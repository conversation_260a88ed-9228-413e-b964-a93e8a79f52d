
import React, { createContext, useContext } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { UserRole } from '@/hooks/useUsers';

interface UserContextType {
  currentUser: any;
  setCurrentUser: (user: any) => void;
  userRole: UserRole | null;
  hasAccess: (requiredRoles: UserRole[]) => boolean;
  canAccessMenuItem: (menuItem: string) => boolean;
  canAccessDashboardCard: (cardType: string) => boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser } = useAuth();

  const userRole = currentUser?.role || null;

  const hasAccess = (requiredRoles: UserRole[]): boolean => {
    if (!userRole) return false;
    return requiredRoles.includes(userRole);
  };

  const canAccessMenuItem = (menuItem: string): boolean => {
    if (!userRole) return false;

    switch (userRole) {
      case 'finance':
        return ['dashboard', 'reports', 'payments'].includes(menuItem);

      case 'factory_supervisor':
        return ['dashboard', 'team-management', 'hackline-count'].includes(menuItem);

      case 'yard_supervisor':
        return ['dashboard', 'team-management', 'hackline-count'].includes(menuItem);

      case 'manager':
        return ![
          'settings'
        ].includes(menuItem);

      case 'admin':
        return true; // Admin has access to everything

      default:
        return false;
    }
  };

  const canAccessDashboardCard = (cardType: string): boolean => {
    if (!userRole) return false;

    switch (userRole) {
      case 'finance':
        return false; // Finance role hides all dashboard cards
      
      case 'factory_supervisor':
        return ['factory-output'].includes(cardType);
      
      case 'yard_supervisor':
        return ['setting-teams', 'dehacking', 'fuel-management', 'pallet-tracking'].includes(cardType);
      
      case 'manager':
        return true; // Manager sees all dashboard cards
      
      case 'admin':
        return true; // Admin sees all dashboard cards
      
      default:
        return false;
    }
  };

  // Dummy setCurrentUser for compatibility (auth handles user state now)
  const setCurrentUser = () => {
    console.warn('setCurrentUser is deprecated. Use AuthContext for authentication.');
  };

  const contextValue: UserContextType = {
    currentUser,
    setCurrentUser,
    userRole,
    hasAccess,
    canAccessMenuItem,
    canAccessDashboardCard,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
