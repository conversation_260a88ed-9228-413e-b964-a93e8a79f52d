
import { QuickAccessCards } from "./QuickAccessCards";
import { TimeRangeSelector } from "./TimeRangeSelector";
import { KeyMetrics } from "./KeyMetrics";
import { AnalyticsCharts } from "./AnalyticsCharts";
import { FuelBunkersDashboard } from "./FuelBunkersDashboard";
import { ProductionLoss } from "./ProductionLoss";
import { DailyActivitySummary } from "./DailyActivitySummary";
import { useState } from "react";
import { useUser } from "@/contexts/UserContext";
import { useDashboardLayout } from "@/contexts/DashboardLayoutContext";
import { SettingSummaryCard } from "./cards/SettingSummaryCard";
import { DehackingSummaryCard } from "./cards/DehackingSummaryCard";

export type TimeRange = "today" | "week" | "month" | "year";

export const DashboardContent = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>("week");
  const { userRole } = useUser();
  const { getGridClassName } = useDashboardLayout();

  // Finance role users should see a message directing them to Reports and Payments
  if (userRole === 'finance') {
    return (
      <div className="space-y-4 sm:space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6 text-center">
          <h2 className="text-lg sm:text-xl font-semibold text-blue-800 mb-2">Welcome to Finance Dashboard</h2>
          <p className="text-sm sm:text-base text-blue-600 mb-4">
            As a Finance user, you have access to Reports and Payments sections.
          </p>
          <p className="text-xs sm:text-sm text-blue-500">
            Use the navigation menu to access Reports and Payments.
          </p>
        </div>
      </div>
    );
  }

  // Check if user is a supervisor (factory or yard)
  const isSupervisor = userRole === 'factory_supervisor' || userRole === 'yard_supervisor';

  // Supervisors get a simplified dashboard with only their cards and activity summary
  if (isSupervisor) {
    return (
      <div className="space-y-4 sm:space-y-6">
        <QuickAccessCards />
        <DailyActivitySummary />
      </div>
    );
  }

  // Full dashboard for admins and managers
  return (
    <div className="space-y-4 sm:space-y-6">
      <QuickAccessCards />

      <div className="flex justify-center">
        <TimeRangeSelector
          selectedRange={selectedTimeRange}
          onRangeChange={setSelectedTimeRange}
        />
      </div>

      <KeyMetrics timeRange={selectedTimeRange} />

      <AnalyticsCharts timeRange={selectedTimeRange} />

      <div className={`grid ${getGridClassName()}`}>
        <FuelBunkersDashboard />
        <ProductionLoss />
        <SettingSummaryCard timeRange={selectedTimeRange} />
        <DehackingSummaryCard timeRange={selectedTimeRange} />
      </div>
    </div>
  );
};
