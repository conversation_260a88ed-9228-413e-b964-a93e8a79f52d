import { supabase } from "@/integrations/supabase/client";
import { ManagementBrickType } from "./managementBrickTypes";
import { Database } from "@/integrations/supabase/types";

export type SettingProductionEntry = Database['public']['Tables']['setting_production_entries']['Row'];

let settingProductionEntries: SettingProductionEntry[] = [];
let listeners: (() => void)[] = [];

function emitChange() {
  for (const listener of listeners) {
    listener();
  }
}

async function fetchSettingProductionEntries() {
    const { data, error } = await supabase.from('setting_production_entries').select('*');
    if (error) {
        console.error("Error fetching setting production entries:", error);
        settingProductionEntries = [];
    } else {
        settingProductionEntries = data || [];
    }
    emitChange();
}

// Initial fetch
fetchSettingProductionEntries();


export async function addSettingProductionEntry(entry: { teamId: string; fireId: string; brickTypeId: string; palletCount: number; hour?: number; date?: string }) {
  console.log('[SettingProductionStore] Input entry:', entry);

  const newEntry = {
    date: entry.date || new Date().toISOString().split('T')[0],
    team_id: entry.teamId,
    fire_id: entry.fireId,
    brick_type_id: entry.brickTypeId,
    pallet_count: entry.palletCount,
    hour: entry.hour,
  };

  console.log('[SettingProductionStore] Formatted entry for database:', newEntry);

  const { data, error } = await supabase.from("setting_production_entries").insert(newEntry).select();
  if (error) {
    console.error("[SettingProductionStore] Database error:", error);
    console.error("[SettingProductionStore] Error details:", {
      message: error.message,
      details: error.details,
      hint: error.hint,
      code: error.code
    });
    throw error;
  }

  console.log('[SettingProductionStore] Successfully inserted:', data);
  await fetchSettingProductionEntries();
}

export function getSettingProductionEntries(): SettingProductionEntry[] {
  return settingProductionEntries;
}

export function subscribeToSettingProduction(listener: () => void) {
  listeners.push(listener);
  return () => {
    listeners = listeners.filter(l => l !== listener);
  };
}

export async function getSettingSummary(
  timeFilter: (date: Date) => boolean,
  allFires: { id: string; name: string }[],
  allBrickTypes: ManagementBrickType[]
) {
  const { data: entries, error } = await supabase.from('setting_production_entries').select('*');

  if (error || !entries) {
    console.error("Error fetching setting production:", error);
    return [];
  }

  const filteredEntries = entries.filter(entry => timeFilter(new Date(entry.date)));

  const summaryByFire: Record<string, { pallets: number; bricks: number }> = {};

  filteredEntries.forEach(entry => {
    if (!summaryByFire[entry.fire_id]) {
      summaryByFire[entry.fire_id] = { pallets: 0, bricks: 0 };
    }

    const brickType = allBrickTypes.find(bt => bt.id === entry.brick_type_id);
    const bricksForEntry = brickType ? entry.pallet_count * brickType.bricks_per_pallet : 0;

    summaryByFire[entry.fire_id].pallets += entry.pallet_count;
    summaryByFire[entry.fire_id].bricks += bricksForEntry;
  });
  
  return Object.entries(summaryByFire).map(([fireId, data]) => {
      const fireInfo = allFires.find(f => f.id === fireId);
      return {
          fireId,
          fireName: fireInfo ? fireInfo.name : 'Unknown Fire',
          ...data,
      }
  });
}
