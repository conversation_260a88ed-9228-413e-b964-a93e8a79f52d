import { supabase } from "@/integrations/supabase/client";

export const debugSupabaseConnection = async () => {
  console.log('🔍 Debugging Supabase connection...');
  
  // Check if supabase client is initialized
  console.log('📊 Supabase client:', supabase);
  console.log('📊 Supabase URL:', supabase.supabaseUrl);
  console.log('📊 Supabase Key:', supabase.supabaseKey ? 'Present' : 'Missing');
  
  try {
    // Test basic connection
    console.log('🔄 Testing basic connection...');
    const { data, error } = await supabase.from('teams').select('count', { count: 'exact' });
    
    if (error) {
      console.error('❌ Basic connection failed:', error);
      console.error('❌ Error code:', error.code);
      console.error('❌ Error message:', error.message);
      console.error('❌ Error details:', error.details);
      console.error('❌ Error hint:', error.hint);
      return false;
    }
    
    console.log('✅ Basic connection successful');
    console.log('📊 Teams count result:', data);
    
    // Test select all teams
    console.log('🔄 Testing select all teams...');
    const { data: allTeams, error: selectError } = await supabase
      .from('teams')
      .select('*');
    
    if (selectError) {
      console.error('❌ Select teams failed:', selectError);
      return false;
    }
    
    console.log('✅ Select teams successful');
    console.log('📋 All teams:', allTeams);
    
    // Test insert capability
    console.log('🔄 Testing insert capability with dummy data...');
    const testTeam = {
      id: 'test-team-' + Date.now(),
      name: 'Test Team ' + Date.now()
    };
    
    const { data: insertData, error: insertError } = await supabase
      .from('teams')
      .insert(testTeam)
      .select()
      .single();
    
    if (insertError) {
      console.error('❌ Insert test failed:', insertError);
      console.error('❌ Insert error code:', insertError.code);
      console.error('❌ Insert error message:', insertError.message);
      return false;
    }
    
    console.log('✅ Insert test successful:', insertData);
    
    // Clean up test team
    console.log('🧹 Cleaning up test team...');
    const { error: deleteError } = await supabase
      .from('teams')
      .delete()
      .eq('id', testTeam.id);
    
    if (deleteError) {
      console.warn('⚠️ Failed to clean up test team:', deleteError);
    } else {
      console.log('✅ Test team cleaned up');
    }
    
    return true;
    
  } catch (error) {
    console.error('💥 Debug test failed:', error);
    return false;
  }
};

// Make it available globally for console testing
(window as any).debugSupabase = debugSupabaseConnection;
