import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface HacklineCount {
  id: number;
  created_at: string;
  date: string;
  count_total: number;
  user_id: string;
  notes?: string;
}

export interface NewHacklineCount {
  date: string;
  count_total: number;
  notes?: string;
}

export function useHacklineCounts() {
  return useQuery({
    queryKey: ["hacklineCounts"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("hackline_counts")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching hackline counts:", error);
          throw new Error(error.message);
        }

        return (data || []) as HacklineCount[];
      } catch (error) {
        console.error("Error in hackline counts query:", error);
        throw error;
      }
    },
    retry: false, // Don't retry if table doesn't exist
  });
}

export function useAddHacklineCount() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (newCount: NewHacklineCount) => {
      if (!currentUser?.id) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("hackline_counts")
        .insert({
          date: newCount.date,
          count_total: newCount.count_total,
          notes: newCount.notes,
          user_id: currentUser.id,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding hackline count:", error);
        throw new Error(error.message);
      }

      return data as HacklineCount;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["hacklineCounts"] });
    },
  });
}
