import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface HacklineCount {
  id: number;
  created_at: string;
  date: string;
  count_total: number;
  user_id: string;
  notes?: string;
}

export interface NewHacklineCount {
  date: string;
  count_total: number;
  notes?: string;
}

export function useHacklineCounts() {
  return useQuery({
    queryKey: ["hacklineCounts"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("hackline_counts")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching hackline counts:", error);

          // If table doesn't exist, try to get data from localStorage
          if (error.code === '42P01' || error.message.includes('relation "hackline_counts" does not exist')) {
            console.log("Table doesn't exist, loading from localStorage");
            const fallbackData = JSON.parse(localStorage.getItem('hackline_counts') || '[]');
            return fallbackData as HacklineCount[];
          }

          throw new Error(error.message);
        }

        return (data || []) as HacklineCount[];
      } catch (error) {
        console.error("Error in hackline counts query:", error);

        // Final fallback to localStorage
        try {
          const fallbackData = JSON.parse(localStorage.getItem('hackline_counts') || '[]');
          console.log("Using localStorage fallback data:", fallbackData);
          return fallbackData as HacklineCount[];
        } catch (localStorageError) {
          console.error("Error reading from localStorage:", localStorageError);
          return [];
        }
      }
    },
    retry: false, // Don't retry if table doesn't exist
  });
}

export function useAddHacklineCount() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (newCount: NewHacklineCount) => {
      if (!currentUser?.id) {
        throw new Error("User not authenticated");
      }

      try {
        // Try to insert into database first
        const { data, error } = await supabase
          .from("hackline_counts")
          .insert({
            date: newCount.date,
            count_total: newCount.count_total,
            notes: newCount.notes,
            user_id: currentUser.id,
          })
          .select()
          .single();

        if (error) {
          console.error("Database error:", error);

          // If table doesn't exist, store in localStorage as fallback
          if (error.code === '42P01' || error.message.includes('relation "hackline_counts" does not exist')) {
            console.log("Table doesn't exist, storing in localStorage as fallback");

            const fallbackData: HacklineCount = {
              id: Date.now(), // Use timestamp as ID
              created_at: new Date().toISOString(),
              date: newCount.date,
              count_total: newCount.count_total,
              notes: newCount.notes || null,
              user_id: currentUser.id,
            };

            // Store in localStorage
            const existingData = JSON.parse(localStorage.getItem('hackline_counts') || '[]');
            existingData.unshift(fallbackData);
            localStorage.setItem('hackline_counts', JSON.stringify(existingData));

            return fallbackData;
          }

          throw new Error(error.message);
        }

        return data as HacklineCount;
      } catch (err) {
        console.error("Error adding hackline count:", err);
        throw err;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["hacklineCounts"] });
    },
  });
}
