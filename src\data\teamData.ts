
import { supabase } from "@/integrations/supabase/client";
import { Employee } from './employeeData';
import { Team } from './fuelBunkersData';
import { v4 as uuidv4 } from "uuid";

export interface TeamWithMembers extends Team {
  members: (Employee & { membershipId: string })[];
}

export const getTeamsWithMembers = async (): Promise<TeamWithMembers[]> => {
  const { data: teams, error: teamsError } = await supabase.from('teams').select('*');
  if (teamsError) throw teamsError;
  if (!teams) return [];

  const { data: memberships, error: membershipsError } = await supabase
    .from('team_memberships')
    .select('id, team_id, employees(*)');
  if (membershipsError) throw membershipsError;

  const teamsWithMembers = teams.map(team => {
    const members = memberships
      ?.filter((m: any) => m.team_id === team.id && m.employees)
      .map((m: any) => ({
        ...(m.employees as Employee),
        membershipId: m.id,
      })) || [];
    return { ...team, members };
  });

  return teamsWithMembers;
};

export const getUnassignedEmployees = async (): Promise<Employee[]> => {
  const { data: allEmployees, error: employeesError } = await supabase.from('employees').select('*');
  if (employeesError) throw employeesError;
  if (!allEmployees) return [];

  const { data: memberships, error: membershipsError } = await supabase.from('team_memberships').select('employee_id');
  if (membershipsError) throw membershipsError;
  if (!memberships) return allEmployees;

  const assignedEmployeeIds = new Set(memberships.map((m: any) => m.employee_id));
  
  return allEmployees.filter(e => !assignedEmployeeIds.has(e.id));
};

export const addTeam = async (name: string) => {
  const { data, error } = await supabase.from('teams').insert([{ id: uuidv4(), name }]).select().single();
  if (error) throw error;
  return data;
};

export const deleteTeam = async (id: string) => {
  const { error } = await supabase.from('teams').delete().eq('id', id);
  if (error) throw error;
};

export const addTeamMember = async ({ teamId, employeeId }: { teamId: string, employeeId: number }) => {
  const { data, error } = await supabase.from('team_memberships').insert([{ team_id: teamId, employee_id: employeeId }]).select().single();
  if (error) throw error;
  return data;
};

export const removeTeamMember = async (membershipId: string) => {
  const { error } = await supabase.from('team_memberships').delete().eq('id', membershipId);
  if (error) throw error;
};

export const getDehackingTeams = async (): Promise<Team[]> => {
  console.log('🔍 Fetching dehacking teams...');

  // First try to get teams by specific IDs
  const { data: teamsByIds, error: idsError } = await supabase
    .from('teams')
    .select('*')
    .in('id', ['dehacking-team-mercy', 'dehacking-team-taurai']);

  if (idsError) {
    console.error('❌ Error fetching teams by IDs:', idsError);
    throw idsError;
  }

  console.log('📋 Teams found by IDs:', teamsByIds);

  // If we found teams by IDs, return them
  if (teamsByIds && teamsByIds.length > 0) {
    console.log('✅ Found teams by IDs:', teamsByIds.length);
    return teamsByIds;
  }

  // Fallback: try to get teams by name
  console.log('🔄 No teams found by IDs, trying by names...');
  const { data: teamsByNames, error: namesError } = await supabase
    .from('teams')
    .select('*')
    .in('name', ['Dehacking Team Mercy', 'Dehacking Team Taurai']);

  if (namesError) {
    console.error('❌ Error fetching teams by names:', namesError);
    throw namesError;
  }

  console.log('📋 Teams found by names:', teamsByNames);

  if (teamsByNames && teamsByNames.length > 0) {
    console.log('✅ Found teams by names:', teamsByNames.length);
    return teamsByNames;
  }

  // If still no teams found, log all teams for debugging
  console.log('⚠️ No dehacking teams found, fetching all teams for debugging...');
  const { data: allTeams, error: allError } = await supabase
    .from('teams')
    .select('*');

  if (!allError) {
    console.log('📋 All teams in database:', allTeams);

    // Look for teams with similar names
    const similarTeams = allTeams?.filter(team =>
      team.name.toLowerCase().includes('dehacking') ||
      team.name.toLowerCase().includes('mercy') ||
      team.name.toLowerCase().includes('taurai')
    );

    if (similarTeams && similarTeams.length > 0) {
      console.log('🔍 Found similar teams:', similarTeams);
      return similarTeams;
    }
  }

  console.log('❌ No dehacking teams found at all');
  return [];
};

export const createDehackingTeams = async () => {
  const teams = [
    { id: 'dehacking-team-mercy', name: 'Dehacking Team Mercy' },
    { id: 'dehacking-team-taurai', name: 'Dehacking Team Taurai' }
  ];

  console.log('Creating dehacking teams...');

  for (const team of teams) {
    try {
      // First check if team already exists
      const { data: existingTeam } = await supabase
        .from('teams')
        .select('id')
        .eq('id', team.id)
        .single();

      if (existingTeam) {
        console.log(`Team ${team.name} already exists`);
        continue;
      }

      // Create the team
      const { data, error } = await supabase
        .from('teams')
        .insert(team)
        .select()
        .single();

      if (error) {
        console.error(`Error creating team ${team.name}:`, error);
        throw error;
      } else {
        console.log(`Successfully created team: ${team.name}`, data);
      }
    } catch (error) {
      console.error(`Failed to create team ${team.name}:`, error);
      throw error;
    }
  }

  console.log('Dehacking teams creation completed');
};

export const isDehackingTeam = (teamId: string): boolean => {
  return teamId === 'dehacking-team-mercy' || teamId === 'dehacking-team-taurai';
};

export const getTeamMembers = async (teamId: string): Promise<Employee[]> => {
  if (!teamId) return [];

  console.log('🔍 Fetching members for team:', teamId);

  // First, let's check if the team exists
  const { data: teamCheck, error: teamError } = await supabase
    .from('teams')
    .select('*')
    .eq('id', teamId)
    .single();

  if (teamError) {
    console.error('❌ Error checking team existence:', teamError);
    console.error('❌ Team ID being searched:', teamId);
  } else {
    console.log('✅ Team found:', teamCheck);
  }

  // Now fetch memberships with detailed logging
  console.log('🔍 Querying team_memberships for team_id:', teamId);
  const { data: memberships, error } = await supabase
    .from('team_memberships')
    .select('id, team_id, employee_id, employees(*)')
    .eq('team_id', teamId);

  if (error) {
    console.error('❌ Error fetching team members:', error);
    throw error;
  }

  console.log('📋 Raw memberships data:', memberships);
  console.log('📊 Memberships count:', memberships?.length || 0);

  // Let's also check all memberships for debugging
  const { data: allMemberships } = await supabase
    .from('team_memberships')
    .select('id, team_id, employee_id, employees(*)');

  console.log('📋 ALL team memberships in database:', allMemberships);

  // Filter memberships that match our team
  const matchingMemberships = allMemberships?.filter(m => m.team_id === teamId);
  console.log('🎯 Memberships matching team ID:', matchingMemberships);

  const members = memberships
    ?.filter((m: any) => m.employees)
    .map((m: any) => {
      console.log('👤 Processing member:', m);
      return m.employees as Employee;
    }) || [];

  console.log('👥 Final team members found:', members.length, members);

  return members;
};
