
import { supabase } from "@/integrations/supabase/client";

export interface DehackingEntry {
  id: number;
  employeeId: number;
  brickTypeId: string;
  palletCount: number;
  date: string;
  hour: number;
  teamId?: string;
}

export interface DehackingEntryFromSupabase {
  id: number;
  employee_id: number;
  brick_type_id: string;
  pallet_count: number;
  date: string;
  hour: number;
  team_id?: string;
  created_at: string;
}

let dehackingEntries: DehackingEntry[] = [];
let subscribers: (() => void)[] = [];

const notifySubscribers = () => {
  subscribers.forEach(callback => callback());
};

export const subscribeToDehacking = (callback: () => void) => {
  subscribers.push(callback);
  return () => {
    subscribers = subscribers.filter(sub => sub !== callback);
  };
};

// Load initial data
const loadDehackingData = async () => {
  try {
    const { data, error } = await supabase
      .from('dehacking_entries')
      .select('*');
    
    if (error) {
      console.error('Error loading dehacking data:', error);
      return;
    }

    dehackingEntries = data?.map(entry => ({
      id: entry.id,
      employeeId: entry.employee_id,
      brickTypeId: entry.brick_type_id,
      palletCount: entry.pallet_count,
      date: entry.date,
      hour: entry.hour,
      teamId: entry.team_id,
    })) || [];
    
    notifySubscribers();
  } catch (error) {
    console.error('Error loading dehacking data:', error);
  }
};

// Initialize data loading
loadDehackingData();

export const addDehackingEntry = async (entry: Omit<DehackingEntry, 'id'>) => {
  try {
    const { data, error } = await supabase
      .from('dehacking_entries')
      .insert({
        employee_id: entry.employeeId,
        brick_type_id: entry.brickTypeId,
        pallet_count: entry.palletCount,
        date: entry.date,
        hour: entry.hour,
        team_id: entry.teamId,
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding dehacking entry:', error);
      throw error;
    }

    const newEntry: DehackingEntry = {
      id: data.id,
      employeeId: data.employee_id,
      brickTypeId: data.brick_type_id,
      palletCount: data.pallet_count,
      date: data.date,
      hour: data.hour,
      teamId: data.team_id,
    };

    dehackingEntries.push(newEntry);
    notifySubscribers();
    return newEntry;
  } catch (error) {
    console.error('Error adding dehacking entry:', error);
    throw error;
  }
};

export const getDehackingSummary = (filter: (date: Date) => boolean) => {
  return dehackingEntries.filter(entry => filter(new Date(entry.date)));
};

export const getDehackingBreakdown = (
  timeFilter: (date: Date) => boolean,
  allBrickTypes: any[]
) => {
  const filteredEntries = dehackingEntries.filter(entry => timeFilter(new Date(entry.date)));
  
  // Group by brick type
  const typeStats: { [key: string]: number } = {};
  
  filteredEntries.forEach(entry => {
    const brickType = allBrickTypes.find(bt => bt.id === entry.brickTypeId);
    if (brickType) {
      const bricksCount = entry.palletCount * brickType.bricks_per_pallet;
      typeStats[entry.brickTypeId] = (typeStats[entry.brickTypeId] || 0) + bricksCount;
    }
  });

  const typeBreakdown = Object.entries(typeStats)
    .map(([brickTypeId, bricks]) => {
      const brickType = allBrickTypes.find(bt => bt.id === brickTypeId);
      return {
        id: brickTypeId,
        name: brickType?.name || `Type ${brickTypeId}`,
        bricks,
      };
    })
    .sort((a, b) => b.bricks - a.bricks);

  return {
    typeBreakdown,
  };
};
