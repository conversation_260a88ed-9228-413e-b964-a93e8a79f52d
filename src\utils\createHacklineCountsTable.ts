import { supabaseAdmin } from "@/integrations/supabase/client";

export const createHacklineCountsTable = async () => {
  try {
    console.log("Creating hackline_counts table...");

    // First, try to create the table using raw SQL
    const { error: createError } = await supabaseAdmin
      .from('hackline_counts')
      .select('id')
      .limit(1);

    // If the table doesn't exist, we'll get an error
    if (createError && createError.code === '42P01') {
      console.log("Table doesn't exist, attempting to create it...");

      // Since we can't run DDL directly, let's try a different approach
      // We'll create a simple test insert to see if the table exists
      const { error: testError } = await supabaseAdmin
        .from('hackline_counts')
        .insert({
          date: '2025-01-01',
          count_total: 1,
          user_id: '00000000-0000-0000-0000-000000000000',
          notes: 'test'
        });

      if (testError) {
        console.error("Table creation needed. Please run the migration manually.");
        console.log("Run this SQL in your Supabase dashboard:");
        console.log(`
-- Create hackline_counts table for tracking brick counts drying in the sun
CREATE TABLE IF NOT EXISTS public.hackline_counts (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  "date" DATE NOT NULL,
  count_total INT NOT NULL,
  user_id UUID NOT NULL REFERENCES public.users(id),
  notes TEXT
);

-- Add RLS policies for hackline_counts
ALTER TABLE public.hackline_counts ENABLE ROW LEVEL SECURITY;

-- Policy to allow all users to view hackline counts (for supervisors)
CREATE POLICY "Allow all users to view hackline counts" ON public.hackline_counts
  FOR SELECT USING (true);

-- Policy to allow authenticated users to insert hackline counts
CREATE POLICY "Allow authenticated users to insert hackline counts" ON public.hackline_counts
  FOR INSERT WITH CHECK (true);

-- Policy to allow authenticated users to update hackline counts
CREATE POLICY "Allow authenticated users to update hackline counts" ON public.hackline_counts
  FOR UPDATE USING (true);
        `);
        throw new Error("Please create the hackline_counts table manually using the SQL above");
      }
    }

    console.log("✅ hackline_counts table is ready!");
    return true;
  } catch (error) {
    console.error("❌ Failed to verify hackline_counts table:", error);
    throw error;
  }
};
