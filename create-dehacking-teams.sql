-- <PERSON><PERSON><PERSON> to create the dehacking teams and add team_id column to dehacking_entries
-- Run this in your Supabase SQL editor

-- First, let's see what teams currently exist
SELECT 'Current teams:' as info;
SELECT * FROM public.teams ORDER BY name;

-- First, add the team_id column to dehacking_entries if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dehacking_entries'
                   AND column_name = 'team_id') THEN
        ALTER TABLE public.dehacking_entries
        ADD COLUMN team_id TEXT REFERENCES public.teams(id);
    END IF;
END $$;

-- Delete any existing teams with similar names but wrong IDs (optional - be careful!)
-- DELETE FROM public.teams WHERE name LIKE '%Dehacking Team%' AND id NOT IN ('dehacking-team-mercy', 'dehacking-team-taurai');

-- Create the two specific dehacking teams with exact IDs and names
INSERT INTO public.teams (id, name) VALUES
('dehacking-team-mercy', 'Dehacking Team Mercy'),
('dehacking-team-taurai', 'Dehacking Team Taurai')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name;

-- Verify the teams were created with correct names
SELECT 'Dehacking teams created:' as info;
SELECT * FROM public.teams WHERE id IN ('dehacking-team-mercy', 'dehacking-team-taurai');

-- Show all teams to verify
SELECT 'All teams after creation:' as info;
SELECT * FROM public.teams ORDER BY name;
