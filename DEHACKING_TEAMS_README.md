# Dehacking Teams Implementation

## Overview
This document describes the implementation of the two special dehacking teams: "Dehacking Team Mercy" and "Dehacking Team Taurai".

## Features Implemented

### 1. Team Management Page
- **Automatic Team Creation**: The two dehacking teams are automatically created when the Team Management page loads
- **15-Member Limit**: Each team is limited to a maximum of 15 members
- **Protected Teams**: Dehacking teams cannot be deleted (delete button is hidden)
- **Visual Identification**: Dehacking teams display a "Dehacking Team" badge for easy identification
- **Member Management**: Full add/remove member functionality with the same UI as regular teams

### 2. Quick Dehacking Entry Form
- **Team Selection Field**: Added right after the Date field as requested
- **Filtered Teams**: Only shows the two dehacking teams in the dropdown
- **Required Field**: Team selection is mandatory for creating dehacking entries
- **Database Integration**: Team ID is stored with each dehacking entry

### 3. Database Schema
- **New Column**: Added `team_id` column to `dehacking_entries` table
- **Foreign Key**: Links dehacking entries to teams table
- **Migration**: Automatic database migration to add the column and create teams

## Technical Implementation

### Files Modified
1. **Database Migration**: `supabase/migrations/20250620000001-add-team-to-dehacking-entries.sql`
2. **Data Layer**: 
   - `src/data/teamData.ts` - Added dehacking team functions
   - `src/data/dehackingStore.ts` - Updated to handle team_id
3. **Hooks**: `src/hooks/useTeams.ts` - Added useDehackingTeams hook
4. **Components**:
   - `src/components/pages/TeamManagementPage.tsx` - Ensures teams are created
   - `src/components/teams/TeamCard.tsx` - Added protection and badges
   - `src/components/dashboard/dialogs/NewQuickDehackingEntryDialog.tsx` - Added team selection
5. **Types**: `src/integrations/supabase/types.ts` - Updated database types

### Key Functions
- `createDehackingTeams()` - Creates the two teams if they don't exist
- `getDehackingTeams()` - Fetches only the dehacking teams
- `isDehackingTeam()` - Utility to check if a team is a dehacking team
- `useDehackingTeams()` - React hook with automatic team creation

## Team Details
- **Team IDs**: 
  - `dehacking-team-mercy` (Dehacking Team Mercy)
  - `dehacking-team-taurai` (Dehacking Team Taurai)
- **Member Limit**: 15 members per team
- **Protection**: Cannot be deleted through the UI
- **Visibility**: Appear in both Team Management page and Quick Dehacking Entry form

## Usage
1. **Team Management**: Navigate to Team Management page to see and manage the teams
2. **Adding Members**: Click "Add Member" on any dehacking team (disabled when at 15 members)
3. **Dehacking Entries**: Use Quick Entry form, select team after date, then proceed with other fields
4. **Data Integrity**: All new dehacking entries must have a team assigned

## Database Setup
If teams don't appear, run the SQL script in `create-dehacking-teams.sql` manually in your Supabase SQL editor.
