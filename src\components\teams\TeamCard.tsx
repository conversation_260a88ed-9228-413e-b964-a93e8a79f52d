
import { useState } from 'react';
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, UserPlus, Trash2, X } from 'lucide-react';
import { TeamWithMembers, isDehackingTeam as checkIsDehackingTeam } from '@/data/teamData';
import { useRemoveTeamMember, useDeleteTeam } from '@/hooks/useTeams';
import { AddMemberDialog } from './AddMemberDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"


interface TeamCardProps {
  team: TeamWithMembers;
}

export const TeamCard = ({ team }: TeamCardProps) => {
  const [isAddMemberOpen, setIsAddMemberOpen] = useState(false);
  const { mutate: removeMember } = useRemoveTeamMember();
  const { mutate: deleteTeam } = useDeleteTeam();

  // Check if this is a protected dehacking team
  const isDehackingTeam = checkIsDehackingTeam(team.id);

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex flex-col gap-2">
            <CardTitle className="flex items-center gap-2"><Users size={20} /> {team.name}</CardTitle>
            {isDehackingTeam && <Badge variant="secondary" className="w-fit">Dehacking Team</Badge>}
          </div>
          {!isDehackingTeam && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Trash2 className="h-4 w-4 text-red-500" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently delete the team "{team.name}" and remove all its members. This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={() => deleteTeam(team.id)} className="bg-red-500 hover:bg-red-600">Delete</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </CardHeader>
        <CardContent>
          <p className="font-semibold mb-2">Members ({team.members.length}/15):</p>
          {team.members.length > 0 ? (
            <ul className="space-y-1">
              {team.members.map(member => (
                <li key={member.id} className="flex items-center justify-between pr-2">
                  <span>{member.name}</span>
                  <Button variant="ghost" size="icon" onClick={() => removeMember(member.membershipId)}>
                    <X className="h-4 w-4 text-gray-500 hover:text-red-500" />
                  </Button>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">No members yet.</p>
          )}
        </CardContent>
        <CardFooter>
          <Button className="w-full" onClick={() => setIsAddMemberOpen(true)} disabled={team.members.length >= 15}>
            <UserPlus className="mr-2 h-4 w-4" /> Add Member
          </Button>
        </CardFooter>
      </Card>
      {isAddMemberOpen && <AddMemberDialog isOpen={isAddMemberOpen} onClose={() => setIsAddMemberOpen(false)} team={team} />}
    </>
  );
};
