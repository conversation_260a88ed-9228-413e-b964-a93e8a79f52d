# How to Create Dehacking Teams

## Problem
You can see Team 1-8 on the Team Management page, but the "Dehacking Team Mercy" and "Dehacking Team Taurai" are not appearing.

## Solution Options

### Option 1: Use the "Create Dehacking Teams" Button (Recommended)
1. **Navigate to Team Management page** in your application
2. **Look for a blue "Create Dehacking Teams" button** next to the "Add Team" button
3. **Click the button** - it will create both teams automatically
4. **Wait for success message** - you should see "Dehacking teams created successfully!"
5. **Refresh the page** if needed to see the new teams

### Option 2: Browser Console Method
1. **Open your browser's Developer Tools** (F12 or right-click → Inspect)
2. **Go to the Console tab**
3. **Type the following command** and press Enter:
   ```javascript
   createDehackingTeams()
   ```
4. **Wait for the success messages** in the console
5. **Refresh the Team Management page** to see the teams

### Option 3: Database Direct Method (If above methods fail)
1. **Open your Supabase dashboard**
2. **Go to SQL Editor**
3. **Run this SQL command**:
   ```sql
   INSERT INTO public.teams (id, name) VALUES 
   ('dehacking-team-mercy', 'Dehacking Team Mercy'),
   ('dehacking-team-taurai', 'Dehacking Team Taurai')
   ON CONFLICT (id) DO NOTHING;
   ```
4. **Refresh your application**

## What You Should See After Creation

### Team Management Page
- **Two new team cards** with the names:
  - "Dehacking Team Mercy"
  - "Dehacking Team Taurai"
- **Blue "Dehacking Team" badges** on these cards
- **No delete button** on these special teams (they're protected)
- **"Members (0/15)" counter** showing the 15-member limit
- **"Add Member" button** to assign employees

### Quick Dehacking Entry Form
- **Team dropdown field** right after the Date field
- **Only shows the two dehacking teams** (not the regular teams)
- **Required field** - must select a team before saving

## Troubleshooting

### If the button doesn't appear:
- Check the browser console for any errors
- Make sure you're on the Team Management page
- Try refreshing the page

### If clicking the button doesn't work:
- Check the browser console for error messages
- Try the Browser Console Method instead
- Check your internet connection

### If teams still don't appear:
- Try refreshing the page
- Check the browser console for any errors
- Use the Database Direct Method as a fallback

## Verification Steps
1. **Team Management page** should show both dehacking teams
2. **Quick Dehacking Entry** should have team selection field
3. **Teams should support 15 members each**
4. **Teams cannot be deleted** (no delete button)

## Need Help?
If none of these methods work, check:
1. Browser console for error messages
2. Network tab for failed requests
3. Supabase dashboard for database connectivity
