
import { Menu, LogOut, Clock, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useActivityTracking } from "@/hooks/useActivityTracking";
import { Badge } from "@/components/ui/badge";

interface HeaderProps {
  onToggleSidebar: () => void;
}

export const Header = ({ onToggleSidebar }: HeaderProps) => {
  const { currentUser, logout, getSessionDuration, loginTime } = useAuth();
  const { getActivitySummary } = useActivityTracking();

  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const activitySummary = getActivitySummary();

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to log out?')) {
      logout();
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'factory_supervisor': return 'Factory Supervisor';
      case 'yard_supervisor': return 'Yard Supervisor';
      case 'admin': return 'Administrator';
      case 'manager': return 'Manager';
      case 'finance': return 'Finance';
      default: return role;
    }
  };

  return (
    <header className="bg-white border-b border-slate-200 px-3 sm:px-4 lg:px-6 py-3 sm:py-4 flex items-center justify-between">
      <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleSidebar}
          className="lg:hidden flex-shrink-0"
          aria-label="Toggle sidebar"
        >
          <Menu size={20} />
        </Button>
        <div className="min-w-0 flex-1">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-800 truncate">
            Welcome, {currentUser?.full_name}
          </h1>
          <p className="text-xs sm:text-sm text-slate-600 truncate">
            Dashboard Overview | <span className="hidden sm:inline">{currentDate}</span>
            <span className="sm:hidden">{new Date().toLocaleDateString()}</span>
          </p>
        </div>
      </div>

      {/* User Info and Session Details */}
      <div className="flex items-center gap-2 sm:gap-3 lg:gap-4 flex-shrink-0">
        {/* Activity Summary - Hidden on mobile */}
        <div className="hidden lg:flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-slate-600">
            <Clock size={16} />
            <span>Session: {getSessionDuration()}</span>
          </div>
          <Badge variant="secondary" className="text-xs">
            {activitySummary.totalActivities} activities today
          </Badge>
        </div>

        {/* User Info - Responsive */}
        <div className="hidden sm:flex items-center gap-2 lg:gap-3 px-2 lg:px-3 py-2 bg-slate-50 rounded-lg">
          <User size={16} className="text-slate-600 flex-shrink-0" />
          <div className="text-sm min-w-0">
            <div className="font-medium text-slate-800 truncate">{currentUser?.full_name}</div>
            <div className="text-slate-500 text-xs lg:text-sm truncate">
              {getRoleDisplayName(currentUser?.role || '')}
            </div>
          </div>
        </div>

        {/* Mobile User Info - Just icon */}
        <div className="sm:hidden flex items-center gap-2 px-2 py-2 bg-slate-50 rounded-lg">
          <User size={16} className="text-slate-600" />
        </div>

        {/* Logout Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="flex items-center gap-2 min-h-[40px]"
        >
          <LogOut size={16} />
          <span className="hidden sm:inline">Logout</span>
        </Button>
      </div>
    </header>
  );
};
