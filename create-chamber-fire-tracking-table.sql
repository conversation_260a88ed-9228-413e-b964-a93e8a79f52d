-- ========================================
-- CHAMBER FIRE TRACKING TABLE SETUP
-- ========================================
-- Copy and paste this ENTIRE script into your Supabase SQL Editor and click RUN

-- Step 1: Create the table
CREATE TABLE IF NOT EXISTS public.chamber_fire_status (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  kiln_id TEXT NOT NULL REFERENCES public.kilns(id),
  chamber_number INT NOT NULL,
  is_burning BOOLEAN NOT NULL DEFAULT false,
  updated_by UUID REFERENCES public.users(id),
  UNIQUE(kiln_id, chamber_number)
);

-- Step 2: Enable Row Level Security
ALTER TABLE public.chamber_fire_status ENABLE ROW LEVEL SECURITY;

-- Step 3: Create policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Allow all users to view chamber fire status" ON public.chamber_fire_status;
DROP POLICY IF EXISTS "Allow authenticated users to update chamber fire status" ON public.chamber_fire_status;

-- Create new policies
CREATE POLICY "Allow all users to view chamber fire status" ON public.chamber_fire_status
  FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to update chamber fire status" ON public.chamber_fire_status
  FOR ALL WITH CHECK (true);

-- Step 4: Create function to update chamber fire status
CREATE OR REPLACE FUNCTION public.update_chamber_fire_status(
  p_kiln_id TEXT,
  p_chamber_number INT,
  p_is_burning BOOLEAN,
  p_user_id UUID DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Insert or update the chamber status
  INSERT INTO public.chamber_fire_status (kiln_id, chamber_number, is_burning, updated_by, updated_at)
  VALUES (p_kiln_id, p_chamber_number, p_is_burning, p_user_id, NOW())
  ON CONFLICT (kiln_id, chamber_number)
  DO UPDATE SET 
    is_burning = EXCLUDED.is_burning,
    updated_by = EXCLUDED.updated_by,
    updated_at = NOW();
END;
$$;

-- Step 5: Verify the table was created
SELECT 'Chamber fire tracking table created successfully!' as status;
SELECT * FROM public.chamber_fire_status LIMIT 1;
