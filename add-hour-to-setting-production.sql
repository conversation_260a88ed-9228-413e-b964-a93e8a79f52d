-- Manual SQL script to add hour column to setting_production_entries table
-- Run this in your Supabase SQL editor if the migration doesn't run automatically

-- Check if the column already exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'setting_production_entries' 
                   AND column_name = 'hour') THEN
        -- Add hour column
        ALTER TABLE public.setting_production_entries 
        ADD COLUMN hour INTEGER;
        
        -- Add comment
        COMMENT ON COLUMN public.setting_production_entries.hour IS 'Hour of the day when production was recorded (0-23)';
        
        RAISE NOTICE 'Hour column added successfully to setting_production_entries table';
    ELSE
        RAISE NOTICE 'Hour column already exists in setting_production_entries table';
    END IF;
END $$;

-- Verify the column was added
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'setting_production_entries' 
ORDER BY ordinal_position;
