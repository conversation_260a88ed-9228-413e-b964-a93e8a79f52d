import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface ChamberFireStatus {
  id: number;
  created_at: string;
  updated_at: string;
  kiln_id: string;
  chamber_number: number;
  is_burning: boolean;
  updated_by: string | null;
}

export function useChamberFireStatus() {
  return useQuery({
    queryKey: ["chamberFireStatus"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("chamber_fire_status")
          .select("*")
          .order("kiln_id", { ascending: true })
          .order("chamber_number", { ascending: true });

        if (error) {
          console.error("Error fetching chamber fire status:", error);
          throw new Error(error.message);
        }

        return (data || []) as ChamberFireStatus[];
      } catch (error) {
        console.error("Error in chamber fire status query:", error);
        // Return empty array if table doesn't exist yet
        return [] as ChamberFireStatus[];
      }
    },
    retry: false,
  });
}

export function useUpdateChamberFireStatus() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async ({
      kilnId,
      chamberNumber,
      isBurning,
    }: {
      kilnId: string;
      chamberNumber: number;
      isBurning: boolean;
    }) => {
      try {
        console.log("🔥 Updating chamber fire status:", { kilnId, chamberNumber, isBurning });

        // Call the database function to update chamber fire status
        const { error } = await supabase.rpc("update_chamber_fire_status", {
          p_kiln_id: kilnId,
          p_chamber_number: chamberNumber,
          p_is_burning: isBurning,
          p_user_id: currentUser?.id || null,
        });

        if (error) {
          console.error("❌ Database error:", error);
          
          // Fallback: try direct insert/update if function doesn't exist
          const { error: fallbackError } = await supabase
            .from("chamber_fire_status")
            .upsert({
              kiln_id: kilnId,
              chamber_number: chamberNumber,
              is_burning: isBurning,
              updated_by: currentUser?.id || null,
              updated_at: new Date().toISOString(),
            });

          if (fallbackError) {
            console.error("❌ Fallback error:", fallbackError);
            throw new Error(fallbackError.message);
          }
        }

        console.log("✅ Chamber fire status updated successfully");
        return { kilnId, chamberNumber, isBurning };
      } catch (err) {
        console.error("💥 Error updating chamber fire status:", err);
        throw err;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chamberFireStatus"] });
    },
  });
}
