import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface ChamberFireStatus {
  id: number;
  created_at: string;
  updated_at: string;
  kiln_id: string;
  chamber_number: number;
  is_burning: boolean;
  updated_by: string | null;
}

export function useChamberFireStatus() {
  return useQuery({
    queryKey: ["chamberFireStatus"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("chamber_fire_status")
          .select("*")
          .order("kiln_id", { ascending: true })
          .order("chamber_number", { ascending: true });

        if (error) {
          console.error("Error fetching chamber fire status:", error);

          // If table doesn't exist, try to get data from localStorage
          if (error.code === '42P01' || error.message.includes('relation "chamber_fire_status" does not exist')) {
            console.log("Table doesn't exist, loading from localStorage");
            const fallbackData = JSON.parse(localStorage.getItem('chamber_fire_status') || '[]');
            return fallbackData as ChamberFireStatus[];
          }

          throw new Error(error.message);
        }

        return (data || []) as ChamberFireStatus[];
      } catch (error) {
        console.error("Error in chamber fire status query:", error);

        // Final fallback to localStorage
        try {
          const fallbackData = JSON.parse(localStorage.getItem('chamber_fire_status') || '[]');
          console.log("Using localStorage fallback data:", fallbackData);
          return fallbackData as ChamberFireStatus[];
        } catch (localStorageError) {
          console.error("Error reading from localStorage:", localStorageError);
          return [];
        }
      }
    },
    retry: false,
  });
}

export function useUpdateChamberFireStatus() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async ({
      kilnId,
      chamberNumber,
      isBurning,
    }: {
      kilnId: string;
      chamberNumber: number;
      isBurning: boolean;
    }) => {
      try {
        console.log("🔥 Updating chamber fire status:", { kilnId, chamberNumber, isBurning });
        console.log("👤 Current user:", currentUser);

        // Always use fallback approach since table might not exist yet
        console.log("💾 Using direct upsert approach...");

        const { data, error } = await supabase
          .from("chamber_fire_status")
          .upsert({
            kiln_id: kilnId,
            chamber_number: chamberNumber,
            is_burning: isBurning,
            updated_by: currentUser?.id || null,
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) {
          console.error("❌ Database error:", error);
          console.error("❌ Error code:", error.code);
          console.error("❌ Error message:", error.message);

          // If table doesn't exist, store in localStorage as fallback
          if (error.code === '42P01' || error.message.includes('relation "chamber_fire_status" does not exist')) {
            console.log("💾 Table doesn't exist, using localStorage fallback...");

            const storageKey = 'chamber_fire_status';
            const existingData = JSON.parse(localStorage.getItem(storageKey) || '[]');

            // Remove existing entry for this kiln/chamber
            const filteredData = existingData.filter((item: any) =>
              !(item.kiln_id === kilnId && item.chamber_number === chamberNumber)
            );

            // Add new entry
            const newEntry = {
              id: Date.now(),
              kiln_id: kilnId,
              chamber_number: chamberNumber,
              is_burning: isBurning,
              updated_by: currentUser?.id || null,
              updated_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
            };

            filteredData.push(newEntry);
            localStorage.setItem(storageKey, JSON.stringify(filteredData));

            console.log("✅ Stored in localStorage:", newEntry);
            return { kilnId, chamberNumber, isBurning };
          }

          throw new Error(error.message);
        }

        console.log("✅ Chamber fire status updated successfully:", data);
        return { kilnId, chamberNumber, isBurning };
      } catch (err) {
        console.error("💥 Error updating chamber fire status:", err);
        throw err;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chamberFireStatus"] });
    },
  });
}
