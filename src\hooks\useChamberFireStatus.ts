import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface ChamberFireStatus {
  id: number;
  created_at: string;
  updated_at: string;
  kiln_id: string;
  chamber_number: number;
  is_burning: boolean;
  updated_by: string | null;
}

export function useChamberFireStatus() {
  return useQuery({
    queryKey: ["chamberFireStatus"],
    queryFn: async () => {
      // Try database first, but always fallback to localStorage
      try {
        const { data, error } = await supabase
          .from("chamber_fire_status")
          .select("*")
          .order("kiln_id", { ascending: true })
          .order("chamber_number", { ascending: true });

        if (!error && data) {
          console.log("✅ Loaded from database:", data);
          return data as ChamberFireStatus[];
        }

        console.log("❌ Database failed, using localStorage:", error);
      } catch (dbError) {
        console.log("❌ Database error, using localStorage:", dbError);
      }

      // Always fallback to localStorage
      try {
        const fallbackData = JSON.parse(localStorage.getItem('chamber_fire_status') || '[]');
        console.log("💾 Using localStorage fallback data:", fallbackData);
        return fallbackData as ChamberFireStatus[];
      } catch (localStorageError) {
        console.error("❌ Error reading from localStorage:", localStorageError);
        return [];
      }
    },
    retry: false,
  });
}

export function useUpdateChamberFireStatus() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async ({
      kilnId,
      chamberNumber,
      isBurning,
    }: {
      kilnId: string;
      chamberNumber: number;
      isBurning: boolean;
    }) => {
      try {
        console.log("🔥 Updating chamber fire status:", { kilnId, chamberNumber, isBurning });
        console.log("👤 Current user:", currentUser);

        // Try database first, but always fallback to localStorage
        console.log("💾 Trying database first...");

        try {
          const { data, error } = await supabase
            .from("chamber_fire_status")
            .upsert({
              kiln_id: kilnId,
              chamber_number: chamberNumber,
              is_burning: isBurning,
              updated_by: currentUser?.id || null,
              updated_at: new Date().toISOString(),
            })
            .select()
            .single();

          if (!error) {
            console.log("✅ Database update successful:", data);
            return { kilnId, chamberNumber, isBurning };
          }

          console.log("❌ Database failed, using localStorage fallback:", error);
        } catch (dbError) {
          console.log("❌ Database error, using localStorage fallback:", dbError);
        }

        // Always use localStorage fallback
        console.log("💾 Using localStorage fallback...");

        const storageKey = 'chamber_fire_status';
        const existingData = JSON.parse(localStorage.getItem(storageKey) || '[]');

        // Remove existing entry for this kiln/chamber
        const filteredData = existingData.filter((item: any) =>
          !(item.kiln_id === kilnId && item.chamber_number === chamberNumber)
        );

        // Add new entry
        const newEntry = {
          id: Date.now(),
          kiln_id: kilnId,
          chamber_number: chamberNumber,
          is_burning: isBurning,
          updated_by: currentUser?.id || null,
          updated_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
        };

        filteredData.push(newEntry);
        localStorage.setItem(storageKey, JSON.stringify(filteredData));

        console.log("✅ Stored in localStorage:", newEntry);
        return { kilnId, chamberNumber, isBurning };

      } catch (err) {
        console.error("💥 Final error:", err);
        throw new Error("Failed to update chamber fire status");
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chamberFireStatus"] });
    },
  });
}
