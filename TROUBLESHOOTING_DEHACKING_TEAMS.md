# Troubleshooting: Dehacking Teams Creation Error

## Current Issue
You're getting the error: "Failed to create dehacking teams. Please check the console for details."

## Step-by-Step Debugging Process

### Step 1: Check the Browser Console
1. **Open Developer Tools** (F12 or right-click → Inspect)
2. **Go to Console tab**
3. **Look for error messages** when you click "Create Dehacking Teams"
4. **Take note of any red error messages**

### Step 2: Test Database Connection
1. **On the Team Management page**, you should now see three buttons:
   - "Test DB" (gray button)
   - "Debug" (yellow button) 
   - "Create Dehacking Teams" (blue button)

2. **Click "Test DB" first** to verify database connectivity
3. **Check console** for connection results
4. **Click "Debug"** for detailed database diagnostics

### Step 3: Common Error Scenarios

#### Scenario A: Database Connection Issues
**Symptoms:** Console shows connection errors, "Test DB" fails
**Solutions:**
- Check your internet connection
- Verify Supabase project is running
- Check environment variables (.env file)

#### Scenario B: Permission Issues
**Symptoms:** Connection works but insert fails with permission errors
**Solutions:**
- Check Supabase RLS (Row Level Security) policies
- Verify your user has insert permissions on teams table

#### Scenario C: Table Schema Issues
**Symptoms:** Insert fails with schema/column errors
**Solutions:**
- Run the database migration manually
- Check if teams table exists and has correct structure

### Step 4: Manual Database Setup (If All Else Fails)

#### Option 1: Supabase SQL Editor
1. **Go to your Supabase dashboard**
2. **Navigate to SQL Editor**
3. **Run this command:**
```sql
-- First, ensure the teams table exists
CREATE TABLE IF NOT EXISTS public.teams (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL
);

-- Insert the dehacking teams
INSERT INTO public.teams (id, name) VALUES 
('dehacking-team-mercy', 'Dehacking Team Mercy'),
('dehacking-team-taurai', 'Dehacking Team Taurai')
ON CONFLICT (id) DO NOTHING;

-- Verify they were created
SELECT * FROM public.teams WHERE id IN ('dehacking-team-mercy', 'dehacking-team-taurai');
```

#### Option 2: Browser Console Method
1. **Open browser console** on your app
2. **Run this command:**
```javascript
// Test the connection first
debugSupabase()

// If connection works, try creating teams
createDehackingTeams()
```

### Step 5: Check for Specific Error Messages

#### "Failed to fetch existing teams"
- Database connection issue
- Check Supabase URL and API key
- Verify teams table exists

#### "Permission denied"
- RLS policy issue
- Check Supabase authentication
- Verify table permissions

#### "Duplicate key value violates unique constraint"
- Teams already exist but aren't showing
- Try refreshing the page
- Check if teams exist in database

#### "Column 'team_id' does not exist"
- Migration hasn't run
- Run the migration manually
- Check database schema

### Step 6: Environment Check

#### Check .env file (if exists):
```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key
```

#### Check Supabase client configuration:
- Look in `src/integrations/supabase/client.ts`
- Verify URL and key are correct

### Step 7: Alternative Creation Methods

#### Method 1: Use Regular Add Team
1. **Click "Add Team"** button
2. **Enter "Dehacking Team Mercy"** as name
3. **Click Add**
4. **Repeat for "Dehacking Team Taurai"**

#### Method 2: Direct Database Insert
Use the SQL commands from Step 4, Option 1

### Step 8: Verification

After successful creation, you should see:
- Two new team cards on Team Management page
- Blue "Dehacking Team" badges
- No delete buttons on these teams
- Teams available in Quick Dehacking Entry form

### Need More Help?

If none of these steps work:
1. **Copy the exact error message** from console
2. **Check network tab** for failed requests
3. **Verify Supabase project status**
4. **Check if other database operations work** (like viewing existing teams)

### Console Commands for Testing

```javascript
// Test database connection
debugSupabase()

// Create teams
createDehackingTeams()

// Check current teams
console.log('Current teams:', teams)
```
