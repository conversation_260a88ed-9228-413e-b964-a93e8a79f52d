
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useQuery, useQueryClient } from "@tanstack/react-query";

import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { getFires } from "@/data/kilnData";
import { getEmployees, Employee } from "@/data/employeeData";
import { addDehackingEntry } from "@/data/dehackingStore";
import { useDehackingTeams, useTeamMembers } from "@/hooks/useTeams";
import { Team } from "@/data/fuelBunkersData";

const mockEmployees = [
  { id: 1, name: "<PERSON>" },
  { id: 2, name: "<PERSON>" },
  { id: 3, name: "<PERSON>" },
  { id: 4, name: "Lisa <PERSON>" },
  { id: 5, name: "Tom <PERSON>" },
];

const hours = [
  "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00",
  "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00"
];

interface NewQuickDehackingEntryDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NewQuickDehackingEntryDialog = ({ isOpen, onClose }: NewQuickDehackingEntryDialogProps) => {
  const queryClient = useQueryClient();
  const [date, setDate] = useState(() => new Date().toISOString().slice(0, 10));
  const [teamId, setTeamId] = useState<string | undefined>();
  const [employeeId, setEmployeeId] = useState<string | undefined>();
  const [brickTypeId, setBrickTypeId] = useState<string | undefined>();
  const [palletCount, setPalletCount] = useState("");
  const [hour, setHour] = useState<string | undefined>();

  const { data: managementBrickTypesData = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({
    queryKey: ['managementBrickTypes'],
    queryFn: getManagementBrickTypes,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: dehackingTeams = [], isLoading: isLoadingTeams } = useDehackingTeams();

  // Get team members for the selected team
  const { data: teamMembers = [], isLoading: isLoadingTeamMembers } = useTeamMembers(teamId);

  // Debug: Log the teams and members data
  console.log('Dehacking teams loaded:', dehackingTeams);
  console.log('Teams loading state:', isLoadingTeams);
  console.log('Selected team ID:', teamId);
  console.log('Team members loaded:', teamMembers);
  console.log('Team members loading state:', isLoadingTeamMembers);

  const handleClose = () => {
    setDate(new Date().toISOString().slice(0, 10));
    setTeamId(undefined);
    setEmployeeId(undefined);
    setBrickTypeId(undefined);
    setPalletCount("");
    setHour(undefined);
    onClose();
  };

  const handleSave = async () => {
    const parsedPallets = parseInt(palletCount);
    if (!teamId || !employeeId || !brickTypeId || !hour || !palletCount || parsedPallets <= 0) {
      toast.error("Please fill out all fields with valid values.");
      return;
    }
    
    try {
      // Convert hour string (e.g., "14:00") to just the hour number (e.g., 14)
      const hourNumber = parseInt(hour.split(':')[0]);
      
      await addDehackingEntry({
        date,
        employeeId: parseInt(employeeId),
        brickTypeId,
        palletCount: parsedPallets,
        hour: hourNumber,
        teamId,
      });

      toast.success("Dehacking entry recorded!");
      queryClient.invalidateQueries({ queryKey: ['dehackingForLoss'] });
      queryClient.invalidateQueries({ queryKey: ['dehackingBreakdown'] });
      handleClose();
    } catch (error) {
      toast.error("Failed to save entry. Please try again.");
      console.error(error);
    }
  };

  const isLoading = isLoadingBrickTypes || isLoadingTeams;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-lg p-6 bg-white rounded-lg">
        <DialogHeader className="mb-4">
          <DialogTitle className="text-xl font-semibold">Quick Dehacking Entry</DialogTitle>
           <button onClick={handleClose} className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>
          <DialogDescription>Record a new dehacking entry.</DialogDescription>
        </DialogHeader>
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading data...</span>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <Label htmlFor="dehackingDate">Date</Label>
              <Input id="dehackingDate" type="date" value={date} onChange={e => setDate(e.target.value)} />
            </div>
            <div>
              <Label>Team</Label>
              <Select value={teamId} onValueChange={(value) => {
                setTeamId(value);
                // Reset employee selection when team changes
                setEmployeeId(undefined);
              }}>
                <SelectTrigger><SelectValue placeholder="Select team" /></SelectTrigger>
                <SelectContent>
                  {dehackingTeams.length === 0 ? (
                    <SelectItem value="no-teams" disabled>No teams available</SelectItem>
                  ) : (
                    dehackingTeams.map(team => (
                      <SelectItem key={team.id} value={team.id}>
                        {team.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {/* Debug info */}
              <div className="text-xs text-gray-500 mt-1">
                Teams loaded: {dehackingTeams.length} | Loading: {isLoadingTeams ? 'Yes' : 'No'}
              </div>
            </div>
            <div>
              <Label>Employee</Label>
              <Select value={employeeId} onValueChange={setEmployeeId} disabled={!teamId}>
                <SelectTrigger><SelectValue placeholder={!teamId ? "Select team first" : "Select employee"} /></SelectTrigger>
                <SelectContent>
                  {!teamId ? (
                    <SelectItem value="no-team" disabled>Please select a team first</SelectItem>
                  ) : teamMembers.length === 0 ? (
                    <SelectItem value="no-members" disabled>No members in this team</SelectItem>
                  ) : (
                    teamMembers.map(emp => (
                      <SelectItem key={emp.id} value={String(emp.id)}>
                        {emp.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {/* Debug info */}
              <div className="text-xs text-gray-500 mt-1">
                Team members: {teamMembers.length} | Loading: {isLoadingTeamMembers ? 'Yes' : 'No'}
              </div>
            </div>
            <div>
              <Label>Brick Type</Label>
              <Select value={brickTypeId} onValueChange={setBrickTypeId}>
                <SelectTrigger><SelectValue placeholder="Select brick type" /></SelectTrigger>
                <SelectContent>
                  {managementBrickTypesData.map(bt => <SelectItem key={bt.id} value={bt.id}>{bt.name}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Hour</Label>
              <Select value={hour} onValueChange={setHour}>
                <SelectTrigger><SelectValue placeholder="Select hour" /></SelectTrigger>
                <SelectContent>
                  {hours.map(h => <SelectItem key={h} value={h}>{h}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="dehackingPallets">Pallet Count</Label>
              <Input id="dehackingPallets" type="number" placeholder="e.g. 10" value={palletCount} onChange={e => setPalletCount(e.target.value)} />
            </div>
          </div>
        )}
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600" disabled={isLoading}>Save Entry</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
